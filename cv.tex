\documentclass[10pt,a4paper]{article}

% Packages
\usepackage[margin=0.5in]{geometry}
\usepackage{enumitem}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{fontawesome5}
\usepackage{hyperref}
\usepackage{array}

% Colors
\definecolor{headercolor}{RGB}{0, 102, 204}
\definecolor{accentcolor}{RGB}{51, 51, 51}
\definecolor{textcolor}{RGB}{64, 64, 64}

% Custom commands
\newcommand{\namesection}[2]{
    \centering{
        \fontsize{24pt}{24pt}\selectfont\scshape\color{headercolor} #1
        \vspace{1pt}
        \\
        \fontsize{11pt}{11pt}\selectfont\color{accentcolor} #2
    } \vspace{3pt}
}

\newcommand{\contactline}[1]{
    \centering{\small\color{textcolor} #1} \vspace{1pt}
}

\newcommand{\sectionsep}{\vspace{3pt}}

% Section formatting
\titleformat{\section}{\large\scshape\raggedright\color{headercolor}}{}{0em}{}[\titlerule]
\titlespacing{\section}{0pt}{4pt}{3pt}

% Remove page numbers
\pagestyle{empty}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=headercolor,
    urlcolor=headercolor,
}

\begin{document}

% Header
\namesection{Harshwardhan Bhaskar}{Data Analyst}

\contactline{
    \faMapMarker* \hspace{1pt} Ranchi, Jharkhand, India \hspace{8pt}
    \faEnvelope \hspace{1pt} <EMAIL> \hspace{8pt}
    \faPhone \hspace{1pt} +91 8651649921
}

\contactline{
    \faLinkedinIn \hspace{1pt} \href{https://www.linkedin.com/in/harshwardhan-bhaskar-991949294}{linkedin.com/in/harshwardhan-bhaskar} \hspace{8pt}
    \faGithub \hspace{1pt} \href{https://github.com/HarshwardhanBhaskar}{github.com/HarshwardhanBhaskar}
}

\sectionsep

% Professional Summary
\section{Professional Summary}
Aspiring Data Analyst with strong analytical mindset and expertise in business intelligence, data visualization, and process optimization. Proficient in SAP modules, Python programming, and Power BI with demonstrated ability to transform complex data into actionable insights. Passionate about leveraging data-driven approaches to solve business challenges and drive operational efficiency.

\sectionsep

% Education
\section{Education}
\textbf{Bachelor of Technology - Computer Science and Engineering} \hfill 2023 -- 2027 \\
\textit{Birla Institute of Technology (BIT), Mesra}

\vspace{2pt}
\textbf{Higher Secondary Education} \hfill 2022 \\
\textit{St Xavier's College, Ranchi} -- Science stream, CBSE Board

\vspace{2pt}
\textbf{Secondary Education} \hfill 2020 \\
\textit{St John's School, Ranchi} -- CBSE Board

\sectionsep

% Experience
\section{Experience}
\textbf{Industrial Training - Data Analyst} \hfill June 2025 \\
\textit{Jindal Steel \& Power Limited} \hspace{8pt} Wire Rod Mill Department \\
\textit{Supervisor: Santosh Kumar Singh (Head - HR \& ES)}
\begin{itemize}[leftmargin=*, itemsep=0pt, parsep=0pt]
\item Completed comprehensive industrial training in Wire Rod Mill operations and data analysis
\item Gained hands-on experience in SAP modules including Purchase Requisition (PR) and Purchase Order (PO) processes
\item Developed and maintained reports, dashboards, and Key Performance Indicators (KPIs) for operational efficiency
\item Demonstrated exceptional performance, punctuality, and professional conduct throughout the training period
\end{itemize}

\sectionsep

% Featured Projects
\section{Featured Projects}
\textbf{Face Detector ML Project} \hfill \href{https://github.com/HarshwardhanBhaskar/Face-detector-ML}{\faGithub \hspace{1pt} GitHub}
\begin{itemize}[leftmargin=*, itemsep=0pt, parsep=0pt]
\item \textbf{Machine Learning Implementation:} Developed face detection system using VGGFace model with OpenCV for real-time face recognition and analysis
\item \textbf{Security Integration:} Implemented Twilio SMS alert system for unauthorized face detection with secure credential management
\item \textbf{Data Processing:} Created comprehensive logging system with CSV data storage for tracking detection events and performance metrics
\item \textbf{Python Development:} Built robust application using Python with scipy for distance calculations and advanced image processing capabilities
\end{itemize}



\sectionsep

% Technical Skills
\section{Technical Skills}
\textbf{Data Analysis \& BI:} Power BI, Excel, SAP Modules (PR/PO), KPI Development \\
\textbf{Programming Languages:} Python, C++, HTML, CSS \\
\textbf{Business Intelligence:} Reports, Dashboards, Data Visualization, Analytics \\
\textbf{Tools \& Platforms:} SAP, Microsoft Excel, Power BI, Data Analysis Tools

\sectionsep

% Core Competencies
\section{Core Competencies}
\begin{itemize}[leftmargin=*, itemsep=0pt, parsep=0pt]
\item \textbf{Data Analysis Expertise:} SAP Module Implementation, Business Intelligence, KPI Development, Report Generation, Dashboard Creation, Data Visualization
\item \textbf{Soft Skills:} Analytical Thinking, Problem-Solving, Process Optimization, Team Collaboration, Attention to Detail, Business Process Understanding
\end{itemize}

\sectionsep

% Achievements
\section{Achievements \& Certifications}
\begin{itemize}[leftmargin=*, itemsep=0pt, parsep=0pt]
\item \textbf{Academic Excellence:} Currently pursuing Computer Science and Engineering degree with strong foundation in programming and software development
\item \textbf{Industrial Training Certification:} Successfully completed industrial training at Jindal Steel \& Power with commendable performance and professional conduct \href{https://www.linkedin.com/posts/harshwardhan-bhaskar-991949294_industrialtraining-jspl-wirerodmill-activity-7361411769216520193-ZjBQ?utm_source=share&utm_medium=member_desktop&rcm=ACoAAEdUsj0BCzUbtzpVjg_nESsNterC-z2SgRY}{\faLinkedinIn \hspace{1pt} Certificate}
\item \textbf{SAP Expertise:} Demonstrated proficiency in SAP modules (PR/PO) and business process optimization through practical implementation
\item \textbf{Data Analytics Portfolio:} Developed comprehensive reports, dashboards, and KPIs demonstrating expertise in business intelligence and data visualization
\end{itemize}

\end{document}
